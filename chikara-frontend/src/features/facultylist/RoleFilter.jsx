const RoleFilter = ({ model, onModelChange }) => {
    const value = (model && model.filter) || "";

    const onChange = ({ target: { value: newValue } }) => {
        onModelChange(
            newValue === "" || !newValue
                ? null
                : {
                      ...(model || {
                          type: "equals",
                      }),
                      filter: newValue,
                  }
        );
    };

    return (
        <div className="ag-floating-filter-input-wrapper">
            <select
                value={value}
                className="ag-input-field-input ag-text-field-input rounded-md border-gray-600 font-bold text-sm focus:border-[#2196f3] lg:py-1 lg:text-base"
                onChange={onChange}
            >
                <option value="">All</option>
                <option value="student">Student</option>
                <option value="prefect">Prefect</option>
                <option value="admin">Staff</option>
            </select>
        </div>
    );
};

export default RoleFilter;
