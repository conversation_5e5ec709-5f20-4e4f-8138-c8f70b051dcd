import { cn } from "@/lib/utils";
import { addMonths, eachDayOfInterval, endOfMonth, format, startOfMonth, subMonths } from "date-fns";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useState } from "react";
import { Tooltip } from "react-tooltip";

// Show 42 dates

const singleEvents = {
    "2023-05-21": {
        title: "Alpha Test 1 Begins",
    },
    "2024-04-20": {
        title: "Alpha Test 2 Begins",
    },
};

const getDaysOfMonth = (date) => {
    const start = startOfMonth(date);
    const end = endOfMonth(date);
    const today = format(new Date(), "yyyy-MM-dd");

    const populateDays = eachDayOfInterval({ start, end }).map((day) => ({
        date: format(day, "yyyy-MM-dd"),
        isCurrentMonth: true,
        isToday: format(day, "yyyy-MM-dd") === today,
    }));

    const currentMonthDays = populateDays.map((day) => ({
        ...day,
        isSelected: day.isToday,
        event: singleEvents[day.date],
    }));

    const prevMonth = subMonths(date, 1);
    const nextMonth = addMonths(date, 1);

    // Adjust the number of days from the previous month as needed
    const daysBefore = eachDayOfInterval({
        start: startOfMonth(prevMonth),
        end: endOfMonth(prevMonth),
    })
        .slice(-start.getDay() - 5)
        .map((day) => ({ date: format(day, "yyyy-MM-dd"), isPrevMonth: true }));

    const daysAfter = eachDayOfInterval({
        start: startOfMonth(nextMonth),
        end: endOfMonth(nextMonth),
    })
        .slice(0, 42 - currentMonthDays.length - daysBefore.length)
        .map((day) => ({ date: format(day, "yyyy-MM-dd"), isNextMonth: true }));

    return [...daysBefore, ...currentMonthDays, ...daysAfter];
};

export default function HomeCalendar() {
    const [currentMonth, setCurrentMonth] = useState(new Date());

    const handlePrevMonth = () => {
        setCurrentMonth(subMonths(currentMonth, 1));
    };

    const handleNextMonth = () => {
        setCurrentMonth(addMonths(currentMonth, 1));
    };

    const days = getDaysOfMonth(currentMonth);
    const monthLabel = format(currentMonth, "MMMM");
    const yearLabel = format(currentMonth, "yyyy");

    return (
        <div className="rounded-lg border border-black px-3 dark:bg-slate-900">
            <div className="mt-4 mb-6 text-center lg:col-start-8 lg:col-end-13 lg:row-start-1 xl:col-start-9">
                <div className="flex items-center px-10 text-gray-900 uppercase tracking-wide dark:text-slate-100">
                    <button
                        type="button"
                        className="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500"
                        onClick={handlePrevMonth}
                    >
                        <span className="sr-only">Previous month</span>
                        <ChevronLeft className="size-5" aria-hidden="true" />
                    </button>
                    <div className="flex-auto text-sm">{monthLabel}</div>
                    <button
                        type="button"
                        className="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500"
                        onClick={handleNextMonth}
                    >
                        <span className="sr-only">Next month</span>
                        <ChevronRight className="size-5" aria-hidden="true" />
                    </button>
                </div>
                <p className="scale-95 text-gray-900 text-xs dark:text-slate-100">{yearLabel}</p>

                <div className="mt-2 grid grid-cols-7 text-gray-500 text-xs leading-6 dark:text-slate-300">
                    <div>M</div>
                    <div>T</div>
                    <div>W</div>
                    <div>T</div>
                    <div>F</div>
                    <div>S</div>
                    <div>S</div>
                </div>
                <div className="isolate mt-2 grid grid-cols-7 gap-px rounded-lg bg-gray-200 text-sm shadow-sm ring-1 ring-gray-200 dark:bg-gray-600 dark:ring-gray-600">
                    {days.map((day, dayIdx) => (
                        <button
                            key={day.date}
                            data-tooltip-id={day.event ? "date-tooltip" : null}
                            data-tooltip-html={`<p>${day?.event?.title}</p>`}
                            type="button"
                            className={cn(
                                "relative py-1.5 hover:bg-gray-100 focus:z-10 dark:hover:bg-gray-800",
                                day.isCurrentMonth ? "bg-white dark:bg-gray-900" : "bg-gray-50 dark:bg-gray-800",
                                (day.isSelected || day.isToday) && "text-stroke-sm",
                                day.isSelected && "text-white",
                                !day.isSelected &&
                                    day.isCurrentMonth &&
                                    !day.isToday &&
                                    "text-gray-900 dark:text-gray-200",
                                !day.isSelected &&
                                    !day.isCurrentMonth &&
                                    !day.isToday &&
                                    "text-gray-400 dark:text-gray-500",
                                day.isToday && !day.isSelected && "text-indigo-600",
                                dayIdx === 0 && "rounded-tl-lg",
                                dayIdx === 6 && "rounded-tr-lg",
                                dayIdx === days.length - 7 && "rounded-bl-lg",
                                dayIdx === days.length - 1 && "rounded-br-lg"
                            )}
                        >
                            <time
                                dateTime={day.date}
                                className={cn(
                                    "mx-auto flex size-7 items-center justify-center rounded-full",
                                    day.isSelected && day.isToday && "bg-indigo-600",
                                    day.isSelected && !day.isToday && "bg-indigo-600"
                                )}
                            >
                                {day.date.split("-").pop().replace(/^0/, "")}
                            </time>

                            {day.event && (
                                <div
                                    className={cn(
                                        day.isToday
                                            ? " -translate-x-1/2 bottom-px left-1/2 h-[0.2rem] w-5 rounded-full bg-blue-500"
                                            : "-translate-x-1/2 bottom-1.5 left-1/2 size-1.5 rounded-full bg-blue-500",
                                        "absolute"
                                    )}
                                ></div>
                            )}
                        </button>
                    ))}
                </div>
            </div>
        </div>
    );
}
