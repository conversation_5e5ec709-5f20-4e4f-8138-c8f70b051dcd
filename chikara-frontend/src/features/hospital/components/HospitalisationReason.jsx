import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Link } from "react-router-dom";

const HospitalisationReason = ({ actionType, attacker }) => {
    const parsedID = parseInt(attacker);
    const { data: user } = useGetUserInfo(parsedID);

    let actionText;
    switch (actionType) {
        case "Crippled by ":
            actionText = "Crippled by ";
            break;
        case "Mugged by ":
            actionText = "Mugged by ";
            break;
        case "Beaten up by ":
            actionText = "Beaten up by ";
            break;
        default:
            actionText = "Beaten up by ";
    }
    return (
        <span className="text-gray-300 dark:text-stroke-sm">
            {actionText}{" "}
            {parsedID === 0 ? (
                <span className="">Anonymous</span>
            ) : (
                <Link className="text-blue-500" to={`/profile/${user?.id}`}>
                    {user?.username}
                </Link>
            )}
        </span>
    );
};

export default HospitalisationReason;
