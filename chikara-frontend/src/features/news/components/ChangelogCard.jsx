import ShinyButton from "@/components/ui/shiny-button";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import clsx from "clsx";
import { Link } from "react-router-dom";
import reactStringReplace from "react-string-replace";

const HighlightedText = ({ text, className }) => {
    const replacedText = reactStringReplace(text, /\{\{(.*?)\}\}/g, (match, i) => (
        <span key={i + text} className={clsx("text-yellow-400", className)}>
            {match}
        </span>
    ));

    return <span>{replacedText}</span>;
};

const NewsTypeBadge = ({ post, className }) => {
    const variants = {
        Patch: "text-white text-stroke-sm bg-blue-600/50 group-hover:text-white group-hover:bg-blue-600 font-semibold py-0.5 px-3 text-sm lg:py-[6px] lg:px-[20px] rounded-[8px] tracking-widest lg:text-base",
        Event: "text-[#a294ff] text-stroke-sm bg-[#8c7aff59]  group-hover:text-white group-hover:bg-[#8c7aff] font-semibold py-0.5 px-3 text-sm lg:py-[6px] lg:px-[20px] rounded-[8px] tracking-widest lg:text-base",
        News: "text-white text-stroke-sm bg-red-500/75 group-hover:text-white group-hover:bg-red-500 font-semibold py-0.5 px-3 text-sm lg:py-[6px] lg:px-[20px] rounded-[8px] tracking-widest lg:text-base",
    };
    const variant = variants[post.tag];
    return <span className={clsx(variant, className)}>{post.tag}</span>;
};

const getLink = (post) => {
    if (post.hideLink) return null;
    if (post.externalLink) return post.externalLink;
    if (post.link) return post.link;
    return `/news/${post.href}`;
};

const ChangelogCard = ({ post, isLast, isFirst }) => {
    if (!post) return null;

    return (
        <Link
            className="group relative flex h-min w-full cursor-pointer flex-col flex-nowrap items-center gap-2.5 px-5 font-display no-underline md:flex lg:w-[720px] lg:flex-row lg:p-0"
            to={getLink(post)}
        >
            <div
                className={clsx(
                    "relative mt-1.5 hidden h-auto w-[170px] flex-row items-start gap-0 self-stretch p-0 lg:flex",
                    isLast ? "" : "-mb-14"
                )}
            >
                <div className="flex flex-col flex-nowrap ">
                    <div className="-left-3 relative z-10 flex h-min w-full flex-col flex-nowrap items-start gap-3.5 p-0 will-change-transform">
                        <div className="relative size-auto">
                            <div className="contents" tabIndex="0">
                                <div className="relative flex size-min flex-col flex-nowrap items-center gap-2.5 px-5">
                                    <p className="relative whitespace-pre text-left">
                                        <NewsTypeBadge post={post} />
                                    </p>
                                </div>
                            </div>
                        </div>
                        <p className="ml-6 relative whitespace-pre text-sm font-body text-stroke-0 text-white/60">
                            {post.date}
                        </p>
                    </div>
                </div>
                <TimelineContent />
            </div>

            <div
                className={clsx(
                    "relative ms-1 flex h-min flex-1 flex-col flex-nowrap items-start justify-center p-0 lg:ml-0 lg:w-px lg:items-center"
                )}
            >
                <TimelineContentWrapper isLast={isLast} isFirst={isFirst}>
                    <div className={clsx("relative flex h-min w-full flex-col flex-nowrap items-start gap-2.5 p-0")}>
                        <div className="relative flex items-center gap-2 lg:hidden">
                            <p>
                                <NewsTypeBadge post={post} className="lg:hidden" />
                            </p>

                            <p className="relative text-sm text-stroke-0 text-white/60">{post.date}</p>
                        </div>

                        <h2 className="relative w-full whitespace-pre-wrap break-words font-bold font-display text-2xl text-white leading-tight">
                            {post.title}
                        </h2>
                        <p className="relative w-full overflow-hidden whitespace-pre-wrap break-words text-white/65">
                            <HighlightedText text={post.description} />
                        </p>

                        {!post.hideLink && (
                            <div className="relative h-[26px] lg:w-[120px]">
                                <div className="contents" tabIndex="0">
                                    <div className="relative flex h-[26px] cursor-pointer flex-row flex-nowrap items-center gap-0 p-0 lg:w-[120px]">
                                        <span className="relative overflow-visible whitespace-pre font-medium text-purple-300">
                                            {post.linkText ? post.linkText : "Read more"}
                                        </span>
                                        <div className="relative h-3.5 w-[34px] overflow-visible">
                                            <svg
                                                className="-translate-x-1/2 -translate-y-1/2 absolute top-[57.14%] left-[29.41%] h-3 w-[7px]"
                                                viewBox="0 0 7 12"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    d="M1.5 2L5.5 6L1.5 10"
                                                    stroke="currentColor"
                                                    strokeWidth="2"
                                                    strokeLinecap="round"
                                                />
                                            </svg>
                                            <div className="-translate-x-1/2 -translate-y-1/2 absolute top-[57.14%] left-[35.29%] size-0.5 bg-purple-300"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                        {/* <ShinyButton text={post.version} /> */}
                        {post.image && (
                            <div
                                className={clsx(
                                    "relative aspect-[2.47/1] w-full overflow-hidden rounded-lg border border-white/10 lg:h-[219px]",
                                    post.imageContainerClass
                                )}
                            >
                                {post.version && (
                                    <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-20 z-10 ">
                                        <ShinyButton text={post.version} />
                                    </div>
                                )}

                                <img
                                    src={post.image}
                                    alt=""
                                    className={clsx(
                                        "size-full rounded-lg object-cover",
                                        post.imageClass,
                                        post.imageClassLarge
                                    )}
                                />
                            </div>
                        )}
                    </div>
                </TimelineContentWrapper>
            </div>
        </Link>
    );
};

const TimelineContentWrapper = ({ children, isLast, isFirst }) => {
    const isMobile = useCheckMobileScreen();
    if (!isMobile) return children;
    return (
        <div className="relative h-full font-body lg:hidden">
            <div
                className={clsx(
                    isLast ? "h-full" : "h-[160%]",
                    isFirst && "mt-8",
                    "absolute border-gray-200 border-s dark:border-gray-700"
                )}
            ></div>
            <div className="ms-8 lg:mb-10">
                <span className="lg:-start-3 -start-2.5 absolute top-10 flex size-[1.35rem] items-center justify-center rounded-full bg-blue-100 ring-8 ring-white lg:size-6 lg:ring-8 dark:bg-blue-900 dark:ring-gray-900">
                    <svg
                        className="size-2.5 text-blue-800 dark:text-blue-300"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                        <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
                    </svg>
                </span>
                {children}
            </div>
        </div>
    );
};

const TimelineContent = () => {
    return (
        <div className="relative hidden h-full border-gray-200 border-s font-body lg:block dark:border-gray-700">
            <div className="ms-8 lg:mb-10">
                <span className="lg:-start-3 -start-2.5 absolute flex size-[1.35rem] items-center justify-center rounded-full bg-blue-100 ring-8 ring-white lg:size-6 lg:ring-8 dark:bg-blue-900 dark:ring-gray-900">
                    <svg
                        className="size-2.5 text-blue-800 dark:text-blue-300"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                        <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
                    </svg>
                </span>
            </div>
        </div>
    );
};

export default ChangelogCard;
