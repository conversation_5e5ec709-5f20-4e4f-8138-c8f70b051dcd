import { Fragment, useState } from "react";
import PurchaseItemModal from "./PurchaseItemModal";
import ShopItem from "./ShopItem";

export const ShopBuyItems = ({ shopData, currentUser }) => {
    const [itemToBuy, setItemToBuy] = useState({
        id: 0,
        cashValue: 0,
        item: {
            image: "",
        },
        name: "",
    });
    const [openModal, setOpenModal] = useState(false);

    const sortedListings = shopData?.shop_listing?.sort((a, b) => a.cost - b.cost);

    return (
        <>
            {shopData?.shop_listing?.length === 0 && (
                <div className="flex">
                    <p className="mx-auto mt-12 text-2xl dark:text-gray-200">No items available!</p>
                </div>
            )}
            <div className="mx-3 mt-4 mb-6 grid grid-cols-3 gap-x-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6 md:mt-6 lg:grid-cols-5 xl:gap-x-3">
                {sortedListings?.map((product) => (
                    <Fragment key={product.id}>
                        <ShopItem product={product} setOpenModal={setOpenModal} setItemToBuy={setItemToBuy} />
                    </Fragment>
                ))}
            </div>
            <PurchaseItemModal
                openModal={openModal}
                setOpenModal={setOpenModal}
                itemToBuy={itemToBuy}
                currentUser={currentUser}
            />
        </>
    );
};
