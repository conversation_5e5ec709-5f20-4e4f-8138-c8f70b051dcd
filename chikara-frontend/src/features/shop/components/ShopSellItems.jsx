import { useState } from "react";
import SellItemModal from "./SellItemModal";
import ShopItemSell from "./ShopItemSell";
import useGetInventory from "@/hooks/api/useGetInventory";

// const isEquipped = (itemId, user) => {
//   return [
//     user.equippedWeaponId,
//     user.equippedRangedWeaponId,
//     user.equippedHeadId,
//     user.equippedChestId,
//     user.equippedHandsId,
//     user.equippedLegsId,
//     user.equippedFeetId,
//     user.equippedFingerId,
//     user.equippedOffhandId,
//   ].includes(itemId);
// };

export const ShopSellItems = ({ currentUser }) => {
    const [itemToSell, setItemToSell] = useState({
        id: 0,
        cashValue: 0,
        item: {
            image: "",
        },
        name: "",
    });
    const [openModal, setOpenModal] = useState(false);

    const { data, isLoading } = useGetInventory();

    const sortedInventory = data?.sort((a, b) => a?.item?.cashValue - b?.item?.cashValue);
    const filteredInventory = sortedInventory?.filter((item) => item?.item?.itemType !== "quest");

    if (isLoading) return "Loading..";

    return (
        <>
            {data?.length === 0 && (
                <div className="flex">
                    <p className="mx-auto mt-12 text-2xl dark:text-gray-200">You have no items to sell!</p>
                </div>
            )}
            <div className="mx-3 mt-4 mb-6 grid grid-cols-3 gap-x-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6 md:mt-6 lg:grid-cols-5 xl:gap-x-3">
                {filteredInventory.map((item) => (
                    <ShopItemSell item={item} setOpenModal={setOpenModal} setItemToSell={setItemToSell} />
                ))}
            </div>
            <SellItemModal openModal={openModal} setOpenModal={setOpenModal} itemToSell={itemToSell} />
        </>
    );
};
