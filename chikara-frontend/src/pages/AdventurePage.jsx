import StrokedText from "@/components/StrokedText";
import { checkLevelGate } from "@/helpers/levelGates";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { useNavigate } from "react-router-dom";

export default function AdventurePage() {
    const { data: currentUser } = useFetchCurrentUser();
    const { isLocked, message: levelGateMessage } = checkLevelGate("rooftop", currentUser?.level || 0);

    const adventures = [
        {
            id: 1,
            name: "Streets",
            description: "Endless Adventure Mode",
            cover: "https://d13cmcqz8qkryo.cloudfront.net/static/backgrounds/exterior/city/street1Day.webp",
            route: "/streets",
            disabled: false,
        },
        {
            id: 2,
            name: "Rooftop Battles",
            description: "Unique Boss Encounters",
            cover: "https://d13cmcqz8qkryo.cloudfront.net/static/backgrounds/exterior/school/schoolroof1Day.webp",
            route: "/rooftop",
            disabled: isLocked,
            disabledText: <p className="text-custom-yellow text-lg">{levelGateMessage}</p>,
        },
        {
            id: 3,
            name: "Story Mode",
            description: "Coming Soon",
            cover: "https://d13cmcqz8qkryo.cloudfront.net/static/backgrounds/exterior/town/townstreet1Day.webp",
            disabled: true,
        },

        {
            id: 4,
            name: "???",
            description: "Coming Soon",
            cover: "https://d13cmcqz8qkryo.cloudfront.net/static/backgrounds/exterior/town/parkNight.webp",
            disabled: true,
        },
    ];

    return (
        <main className="-mx-4 md:-mt-6 mb-4 flex-1 pb-8 md:mx-auto md:mb-0 md:max-w-6xl">
            <div className="mt-6 md:mt-8 md:px-8">
                <ul className="mx-8 grid grid-cols-1 gap-4 md:mx-10 md:h-[60vh] md:grid-cols-2 md:gap-5">
                    {adventures.map((adventure) => (
                        <AdventureCard key={adventure.id} adventure={adventure} />
                    ))}
                </ul>
            </div>
        </main>
    );
}

function AdventureCard({ adventure }) {
    const navigate = useNavigate();
    const backgroundImageStyle = {
        backgroundImage: `url(${adventure.cover})`,
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center center",
        backgroundSize: "cover",
        backgroundColor: "black",
        opacity: "1",
        width: "100%",
        height: "100%",
        position: "absolute",
        top: "0",
        left: "0",
        borderRadius: "0.375rem",
    };
    return (
        <li
            disabled={adventure.disabled}
            className={`-mx-5 relative col-span-1 flex h-full select-none flex-col rounded-md bg-white text-center shadow ring-1 ring-slate-700 md:mx-0 ${
                adventure.disabled ? " opacity-75" : "cursor-pointer hover:ring-2 hover:ring-blue-500"
            }`}
            onClick={() => !adventure.disabled && navigate(adventure.route)}
        >
            <div className={adventure.disabled ? "grayscale" : ""} style={backgroundImageStyle}></div>
            <div className="relative flex flex-1 flex-col p-5 ">
                <div className="blackImageOverlay rounded-md"></div>
                <div className="dottedOverlay z-10 opacity-50"></div>
                <div className="z-20 m-auto">
                    <h1
                        className={cn(
                            adventure.disabled && "grayscale",
                            "mt-4 mb-2 font-medium font-accent text-4xl text-custom-yellow text-stroke-s-md uppercase"
                        )}
                    >
                        <StrokedText className="text-custom-yellow">{adventure.name}</StrokedText>
                    </h1>
                    <p className="mb-5 font-lili text-white">{adventure.description}</p>
                    {adventure.disabled && adventure.disabledText ? adventure.disabledText : null}
                </div>
            </div>
        </li>
    );
}
