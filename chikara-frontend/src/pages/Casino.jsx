// import Roulette from "./RouletteWheel";
import useGameConfig from "@/hooks/useGameConfig";
import Lottery from "../features/casino/components/Lottery";
import Slots from "../features/casino/components/Slots";

export default function Casino() {
    const { SLOTS_MAX_BET, CASINO_DISABLED, LOTTERY_TICKET_COST, LOTTERY_DISABLED } = useGameConfig();

    if (CASINO_DISABLED) {
        return (
            <div className="mt-10 flex flex-col dark:text-slate-200">
                <div className="mx-auto text-center">
                    <h2 className="text-xl">Casino currently Disabled</h2>
                    <p>Please return later.</p>
                </div>
            </div>
        );
    }
    return (
        <div className="md:-mt-2 mt-2 flex flex-col-reverse gap-4 md:mx-auto md:max-w-6xl md:px-8 md:pb-8 lg:flex-col lg:gap-8 dark:text-white">
            <div className="mx-2 rounded-lg border border-gray-600 bg-slate-900 pt-4 lg:mx-auto lg:pl-24">
                <Slots SLOTS_MAX_BET={SLOTS_MAX_BET} />
            </div>
            {/* <Roulette /> */}
            <div className="mx-2 rounded-lg border border-gray-600 bg-slate-900 px-4 py-3 lg:mx-auto lg:w-fit lg:p-5">
                <p className="text-center font-accent text-3xl">Daily Lottery</p>
                {LOTTERY_DISABLED && <p className="mt-2 text-center text-red-500 text-xl">Currently Disabled</p>}
                <Lottery ticketCost={LOTTERY_TICKET_COST} disabled={LOTTERY_DISABLED} />
            </div>
        </div>
    );
}
