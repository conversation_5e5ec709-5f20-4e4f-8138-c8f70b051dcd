import { useNormalStore } from "@/app/store/stores";
import arcadeIcon from "@/assets/icons/arcade.png";
import auctionIcon from "@/assets/icons/auction.webp";
import bankIcon from "@/assets/icons/bankicon.webp";
import bossIcon from "@/assets/icons/boss2.webp";
import bountyIcon from "@/assets/icons/bounty.webp";
import casinoIcon from "@/assets/icons/casino.webp";
import classroomIcon from "@/assets/icons/classroom.webp";
import currentJobIcon from "@/assets/icons/currentJob.webp";
import gymIcon from "@/assets/icons/gymicon.webp";
import hospitalIcon from "@/assets/icons/hospitalicon.webp";
import housingIcon from "@/assets/icons/housing.webp";
import jailIcon from "@/assets/icons/jailicon.webp";
import jobsIcon from "@/assets/icons/jobs.webp";
import leaderboardIcon from "@/assets/icons/leaderboard.webp";
import libraryIcon from "@/assets/icons/libraryicon.webp";
import premiumIcon from "@/assets/icons/premium.webp";
import referIcon from "@/assets/icons/refer.webp";
import rooftopIcon from "@/assets/icons/rooftop.webp";
import shoelockerIcon from "@/assets/icons/shoelockericon.webp";
import shopsIcon from "@/assets/icons/shopsicon.webp";
import shrineIcon from "@/assets/icons/shrineicon.webp";
import springsIcon from "@/assets/icons/springsicon.webp";
import storyIcon from "@/assets/icons/story.webp";
import studentcouncilIcon from "@/assets/icons/studentcouncil.webp";
import facultyListIcon from "@/assets/icons/studentlisticon.webp";
import workshopIcon from "@/assets/icons/workshop.webp";
import arrow from "@/assets/images/UI/arrow.gif";
import { levelGates } from "@/helpers/levelGates";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { Disclosure } from "@headlessui/react";
import { ChevronUp } from "lucide-react";
import { Fragment } from "react";
import { Link } from "react-router-dom";
import Explore from "./Explore";
import School from "./School";

const checkGate = (page, currentUser) => {
    if (!page?.levelGate) return false;

    const levelGate = levelGates(page?.levelGate, 0, currentUser?.level);
    if (levelGate) return levelGate;

    return false;
};

export default function CombinedExplore() {
    const { data: currentUser } = useFetchCurrentUser();
    const { SHRINE_DISABLED } = useGameConfig();
    const isMobile = useCheckMobileScreen();
    const { craftCollectReady } = useNormalStore();
    if (!isMobile) return <Explore />;

    const explorePages = [
        { name: "Training", icon: gymIcon, link: "/training", construction: false },
        { name: "Shops", icon: shopsIcon, link: "/shops" },
        { name: "Bank", icon: bankIcon, link: "/bank" },
        {
            name: "Market",
            icon: auctionIcon,
            link: "/market",
            levelGate: "marketLVL",
        },
        {
            name: "Shrine",
            icon: shrineIcon,
            link: "/shrine",
            disabled: SHRINE_DISABLED,
        },
        { name: "Hospital", icon: hospitalIcon, link: "/hospital" },
        { name: "Jail", icon: jailIcon, link: "/jail" },

        { name: "Casino", icon: casinoIcon, link: "/casino", construction: false },
        {
            name: "Part-Time Job",
            icon: currentUser.jobId > 0 ? currentJobIcon : jobsIcon,
            link: currentUser.jobId > 0 ? "/job" : "/joblistings",
            levelGate: "jobLVL",
        },
        {
            name: "Arcade",
            icon: arcadeIcon,
            link: "/arcade",
            construction: false,
            levelGate: "arcadeLVL",
        },

        {
            name: "Housing",
            icon: housingIcon,
            link: "/housing",
            construction: true,
        },
        {
            name: "World Boss",
            icon: bossIcon,
            link: "/worldboss",
            construction: true,
        },

        {
            name: "Hot Springs",
            icon: springsIcon,
            link: "/springs",
            construction: true,
        },
    ];

    const schoolPages = [
        {
            name: "Faculty List",
            icon: facultyListIcon,
            link: "/facultylist",
            construction: false,
        },
        {
            name: "Mission Board",
            icon: classroomIcon,
            link: "/missions",
        },
        {
            name: "Workshop",
            icon: workshopIcon,
            link: "/workshop",
            construction: false,
            levelGate: "craftingLVL",
        },
        {
            name: "Courses",
            icon: storyIcon,
            link: "/courses",
            construction: false,
            levelGate: "coursesLVL",
        },
        {
            name: "Bounty Board",
            icon: bountyIcon,
            link: "/bountyboard",
        },
        {
            name: "Leaderboard",
            icon: leaderboardIcon,
            link: "/leaderboard",
            construction: false,
        },
        {
            name: "Classroom",
            icon: classroomIcon,
            link: "/classroom",
        },
        {
            name: "Shoe Locker",
            icon: shoelockerIcon,
            link: "/shoelocker",
            construction: true,
        },
        {
            name: "Student Council",
            icon: studentcouncilIcon,
            link: "/studentcouncil",
            construction: true,
        },
        {
            name: "Suggestions",
            icon: studentcouncilIcon,
            link: "/suggestions",
        },
    ];

    return (
        <div className=" bg-gray-300 text-right shadow-sm md:mb-0 md:rounded-md dark:bg-gray-700">
            <Disclosure defaultOpen as="div" className="border-slate-700 border-b">
                {({ open }) => (
                    <>
                        <Disclosure.Button className="flex w-full justify-between bg-purple-200 px-4 py-2 text-left font-medium text-lg text-slate-900 hover:bg-purple-200 focus:outline-hidden focus-visible:ring-3 focus-visible:ring-slate-500/75 dark:bg-slate-800 dark:text-slate-200 dark:text-stroke-sm">
                            <h2 className="mx-auto text-2xl text-slate-200 font-display uppercase">City</h2>
                            <ChevronUp className={`${open && "rotate-180"} my-auto size-6 text-slate-500`} />
                        </Disclosure.Button>
                        <Disclosure.Panel className="bg-slate-900 py-4 text-gray-500 text-sm dark:border-slate-600 dark:text-slate-300">
                            <ListView pages={explorePages} currentUser={currentUser} />
                        </Disclosure.Panel>
                    </>
                )}
            </Disclosure>
            <Disclosure defaultOpen as="div" className="">
                {({ open }) => (
                    <>
                        <Disclosure.Button className="flex w-full justify-between bg-purple-200 px-4 py-2 text-left font-medium text-lg text-slate-900 hover:bg-purple-200 focus:outline-hidden focus-visible:ring-3 focus-visible:ring-slate-500/75 dark:bg-slate-800 dark:text-slate-200 dark:text-stroke-sm">
                            <h2 className="mx-auto text-2xl text-slate-200 font-display uppercase">Campus</h2>
                            <ChevronUp className={`${open ? "rotate-180" : ""} my-auto size-6 text-slate-500`} />
                        </Disclosure.Button>
                        <Disclosure.Panel className="bg-slate-900 py-4 text-gray-500 text-sm dark:border-slate-600 dark:text-slate-300">
                            <ListView
                                pages={schoolPages}
                                currentUser={currentUser}
                                craftCollectReady={craftCollectReady}
                            />
                        </Disclosure.Panel>
                    </>
                )}
            </Disclosure>
        </div>
    );
}

const ListView = ({ pages, currentUser, craftCollectReady }) => {
    return (
        <ul className="grid grid-cols-4 gap-3 px-1.5">
            {pages.map((page, i) => (
                <Fragment key={page.link}>
                    {page.construction ? null : (
                        <li key={page.link}>
                            {checkGate(page, currentUser) ? (
                                <ListItemGated
                                    page={page}
                                    i={i}
                                    currentUser={currentUser}
                                    craftCollectReady={craftCollectReady}
                                />
                            ) : (
                                <ListItem
                                    page={page}
                                    i={i}
                                    currentUser={currentUser}
                                    craftCollectReady={craftCollectReady}
                                />
                            )}
                        </li>
                    )}
                </Fragment>
            ))}
        </ul>
    );
};

const ListItem = ({ page, i, currentUser, craftCollectReady }) => {
    const tutorialArrow = false;
    return (
        <Link to={page.link}>
            <div
                className={cn(
                    "relative flex h-24 w-full flex-col rounded-md border-2 border-slate-900 bg-gray-800 p-1.5 shadow-xl ring-3 ring-slate-600",
                    i < 4 && "",
                    page.construction && "brightness-[0.35]"
                )}
            >
                {tutorialArrow && (
                    <img
                        className="-rotate-90 -translate-x-1/2 -top-16 absolute left-[52%] size-24 scale-75"
                        src={arrow}
                        alt=""
                    />
                )}

                {page?.showNotification > currentUser?.user_achievements?.examsCompleted && (
                    <div className="absolute top-0.5 right-0.5 z-40 inline-block size-4 animate-pulse rounded-full bg-sky-500 align-middle md:size-4"></div>
                )}
                <div>
                    <img className={`mx-auto h-14 w-auto object-cover`} src={page.icon} alt="" />

                    <p
                        className={cn(
                            "mx-auto text-center text-stroke-md uppercase dark:text-yellow font-display text-xs"
                        )}
                    >
                        {page.name}
                        {page.name === "Workshop" && craftCollectReady && (
                            <div className="absolute top-1 right-1 z-40 size-3 rounded-full bg-red-500 align-middle md:size-4"></div>
                        )}
                    </p>
                </div>
            </div>
        </Link>
    );
};

const ListItemGated = ({ page, i, currentUser }) => {
    const hasGate = checkGate(page, currentUser);
    return (
        <div
            style={{
                pointerEvents: "none",
            }}
        >
            <div
                className={cn(
                    "font-display flex h-24 w-full flex-col rounded-md border-2 border-slate-900 bg-slate-800 shadow-xl ring-3 ring-slate-600",
                    i < 4 && ""
                )}
            >
                <img className={`m-auto h-12 w-auto object-cover brightness-50`} src={page.icon} alt="" />
                {hasGate === "quest" ? (
                    <p className="mx-auto mb-1 text-center text-stroke-0 text-xs uppercase dark:text-slate-400">
                        Unlocked via <span className="text-indigo-400">task</span>
                    </p>
                ) : (
                    <p className="mx-auto mb-1 text-center text-stroke-0 text-xs uppercase dark:text-slate-400">
                        Unlocked at level <span className="text-indigo-400">{hasGate}</span>
                    </p>
                )}
            </div>
        </div>
    );
};
