import { Link } from "react-router-dom";

function ConstructionPage() {
    return (
        <div className="App mx-auto h-full min-h-full rounded-lg px-4 py-5 sm:px-6 sm:py-16 md:place-items-center lg:px-8 dark:bg-transparent">
            <img
                className="mx-auto h-[400px] md:mb-12 md:h-[450px]"
                src="https://d13cmcqz8qkryo.cloudfront.net/static/characters/Izumi/embarrassed.webp"
                alt=""
            />
            <div className="mx-auto max-w-max">
                <main className="sm:flex">
                    <div className="sm:ml-6">
                        <div className="sm:border-gray-200 sm:border-l sm:pl-6">
                            <h1 className="font-accent text-4xl text-gray-900 tracking-tight sm:text-5xl dark:text-indigo-500 dark:text-stroke-sm dark:shadow-xl ">
                                Under Construction
                            </h1>
                            <p className="mt-1 text-base text-gray-500 dark:text-gray-300">Please come back later.</p>
                        </div>
                        <div className="mt-4 flex space-x-3 sm:border-transparent sm:border-l sm:pl-6 md:mt-10">
                            <Link
                                to="/"
                                className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                                Go back home
                            </Link>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    );
}

export default ConstructionPage;
