import greenTickImg from "@/assets/icons/UI/greenTick.png";
import Button from "@/components/Buttons/Button";
import Spinner from "@/components/Spinners/Spinner";
import { Progress } from "@/components/ui/progress";
import useGetDailyQuests from "@/features/dailytask/api/useGetDailyQuests";
import dailyChest from "@/features/dailytask/dailychest.png";
import lockedChest from "@/features/dailytask/lockedchest.png";
import { getNextMidnightDate, todayString } from "@/helpers/dateHelpers";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { getObjectiveText, objectiveText } from "@/hooks/useGetQuestObjectiveText";
import { cn } from "@/lib/utils";
import { CountdownTimer } from "../components/Layout/CountdownTimer";
import { useClaimCompletionReward } from "../features/dailytask/api/useClaimCompletionReward";
import { useCompleteDailyQuest } from "../features/dailytask/api/useCompleteDailyQuest";
import DailyRewardDisplay from "../features/dailytask/components/DailyRewardDisplay";

const midnightDate = getNextMidnightDate();

const getBackgroundColor = (questStatus) => {
    switch (questStatus) {
        case "complete":
            return "bg-green-500";
        case "ready_to_complete":
            return "bg-yellow-300";
        case "in_progress":
            return "bg-zinc-300";
        default:
            return "bg-[#15151d]";
    }
};

const Dailies = () => {
    const { data: dailyQuests, isLoading } = useGetDailyQuests({
        select: (data) => data.slice(0, 3),
    });
    const { data: currentUser } = useFetchCurrentUser();
    const completedQuests = dailyQuests?.filter((quest) => quest.questStatus === "complete");
    const mutation = useCompleteDailyQuest();
    const claimCompletionReward = useClaimCompletionReward();

    const isChestClaimed = currentUser?.dailyQuestsRewardClaimed === todayString;

    const chestUnlocked = completedQuests?.length >= dailyQuests?.length;

    if (isLoading) return <Spinner center />;

    return (
        <div className="relative mx-auto max-w-2xl md:h-full md:rounded-lg md:bg-gray-700">
            <div className="relative mb-2 flex h-12 justify-between border-black border-y-2 bg-[#343549] p-1 px-4 pb-1.5 md:rounded-t-lg -m-2">
                <h1 className="my-auto font-body font-semibold text-lg text-stroke-s-sm text-white">Daily Tasks</h1>
                <div className="relative my-auto h-7 w-auto">
                    <img
                        className="h-full w-auto"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/AdXczms.png`}
                        alt=""
                    />
                    <p
                        data-testid="countdown-timer"
                        className="absolute top-[0.3rem] right-[2.2rem] text-sm text-white font-display"
                    >
                        {" "}
                        <CountdownTimer showHours targetDate={midnightDate} showSeconds={false} />
                    </p>
                </div>
                <div className="absolute bottom-0 left-0 h-1 w-full bg-[#272839]"></div>
            </div>
            <div className="mx-3 md:p-4 md:pb-12">
                <div className="mb-4 flex h-24 w-full gap-4 rounded-lg border-2 border-[#00ccff] bg-[#0099ff] p-2 ring-2 ring-black">
                    <div className="my-auto flex w-4/6 flex-col gap-1">
                        <p className="font-body font-semibold text-base text-stroke-s-sm text-white">
                            Complete all of the daily tasks
                        </p>
                        <ProgressBar minValue={completedQuests?.length} maxValue={dailyQuests?.length} />
                    </div>
                    <div className={cn("relative my-auto mr-5 ml-auto size-[4.7rem] md:mr-12")}>
                        {chestUnlocked && isChestClaimed && (
                            <img
                                className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 z-30"
                                src={greenTickImg}
                                alt=""
                            />
                        )}

                        {chestUnlocked ? (
                            <img
                                src={dailyChest}
                                alt=""
                                className={cn(
                                    isChestClaimed ? "opacity-50" : "",
                                    "absolute z-20 h-full w-auto cursor-pointer"
                                )}
                                onClick={() => claimCompletionReward.mutate({})}
                            />
                        ) : (
                            <img className="absolute z-20 h-full w-auto" src={lockedChest} alt="" />
                        )}

                        <img
                            className="absolute z-10 size-full scale-150"
                            src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/kwFhD5T.png`}
                            alt=""
                        />
                        {chestUnlocked && !isChestClaimed && (
                            <img
                                className="absolute z-10 size-full scale-150"
                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/5I89C8Q.png`}
                                alt=""
                            />
                        )}
                    </div>
                </div>

                <div className="flex flex-col gap-4">
                    {dailyQuests?.map((quest) => (
                        <div
                            key={quest.id}
                            className={cn(
                                "h-24 w-full gap-4 rounded-lg border-2 border-black",
                                getBackgroundColor(quest.questStatus)
                            )}
                        >
                            <div className="flex size-full flex-row gap-4 p-3">
                                <div className="w-20">
                                    <DailyRewardDisplay quest={quest} />
                                </div>
                                <div className="flex w-full gap-4">
                                    <div className="flex w-full flex-col gap-1">
                                        <div
                                            className={cn(
                                                objectiveText[quest.objectiveType].length > 30
                                                    ? "text-sm"
                                                    : "text-base",
                                                "font-bold text-slate-900! text-stroke-0"
                                            )}
                                        >
                                            {getObjectiveText(quest)}
                                        </div>
                                        <div className="w-full">
                                            <ProgressBar
                                                minValue={Math.min(quest.count, quest.quantity)}
                                                maxValue={quest.quantity}
                                                className="w-full! font-display"
                                            />
                                        </div>
                                    </div>
                                    <div className="my-auto ml-auto w-auto ">
                                        {quest.questStatus === "complete" ? (
                                            <div className="h-full w-20">
                                                <img
                                                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/M3NQxxd.png`}
                                                    alt=""
                                                />
                                            </div>
                                        ) : (
                                            <div className="h-full w-20">
                                                <Button
                                                    disabled={quest.questStatus !== "ready_to_complete"}
                                                    className="w-full font-display"
                                                    onClick={() => mutation.mutate({ id: quest.id })}
                                                >
                                                    Claim
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            {/* <DailyTasksTabs/> */}
        </div>
    );
};

const ProgressBar = ({ minValue, maxValue, className }) => {
    const percentValue = ((minValue / maxValue) * 100).toFixed(0);
    return (
        <div className={cn("flex w-5/6 flex-row justify-center gap-4", className)}>
            <Progress
                barClassName="bg-[#00d200]"
                className="bg-[#292939]! my-auto h-6 rounded-[5px] border-2 border-black font-display"
                value={percentValue}
                displayText={`${minValue}/${maxValue}`}
                backGradient="bg-[#15151d]"
                frontGradient="bg-[#0ff907]"
            />
        </div>
    );
};

const DailyTasksTabs = () => {
    return (
        <div className="flex h-14 gap-2 scale-[0.9] fixed bottom-28 md:hidden">
            <button className="relative text-stroke-s-sm cursor-pointer">
                <p className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-xl">Daily Tasks</p>
                <img
                    className="h-full w-auto"
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/oFP80Gk.png`}
                    alt=""
                />
            </button>
            <button className="relative text-stroke-s-sm opacity-75 cursor-pointer">
                <p className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-xl text-gray-400">
                    Achievements
                </p>
                <img
                    className="h-full w-auto"
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Xz4xElI.png`}
                    alt=""
                />
            </button>
        </div>
    );
};

export default Dailies;
